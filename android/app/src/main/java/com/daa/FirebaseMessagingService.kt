package com.daa

import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.salesforce.marketingcloud.sfmcsdk.SFMCSdk
import android.util.Log
import com.salesforce.marketingcloud.MarketingCloudSdk
import com.salesforce.marketingcloud.MCLogListener
import com.salesforce.marketingcloud.messages.push.PushMessageManager
import com.salesforce.marketingcloud.sfmcsdk.components.logging.LogLevel
import com.salesforce.marketingcloud.sfmcsdk.components.logging.LogListener
import com.salesforce.marketingcloud.notifications.NotificationManager

class MyFirebaseMessagingService : FirebaseMessagingService() {
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        // Pass token to Salesforce SDK
        Log.d("~#SdkState", "New FCM token: $token")
        SFMCSdk.requestSdk { sdk ->
            sdk.mp {
                it.pushMessageManager.setPushToken(token)
            }
            val sdkStateJson = sdk.getSdkState()  // This returns a JSONObject
            val sdkStateString = sdkStateJson.toString()

            // Optionally, log or use it
            Log.i("~#SdkState", sdkStateString)
            sdk.mp {
                it.pushMessageManager.pushToken?.let {
                        token -> Log.i("~#SdkState", token)
                }
            }
            val pushState = sdkStateJson.optJSONObject("PUSH")
            pushState?.let {
                // General Troubleshooting
                Log.i("~#SdkState", "initConfig: ${it.opt("initConfig")}")
                Log.i("~#SdkState", "initStatus: ${it.opt("initStatus")}")
                Log.i("~#SdkState", "PushMessageManager: ${it.optJSONObject("PushMessageManager")?.toString(2)}")
                Log.i("~#SdkState", "RegistrationManager: ${it.optJSONObject("RegistrationManager")?.toString(2)}")

                // Troubleshoot InApp Messages
                Log.i("~#SdkState", "InAppMessageManager: ${it.optJSONObject("InAppMessageManager")?.toString(2)}")

                val inAppMessages = it.optJSONObject("InAppMessageManager")?.optJSONArray("messages")
                Log.i("~#SdkState", "InApp Messages: ${inAppMessages?.toString(2)}")

                // Get Everything
                Log.i("~#SdkState", "InApp Events: ${it.optJSONObject("Event")?.toString(2)}")
            } ?: Log.w("~#SdkState", "PUSH object not found in SDK state")
        }
    }

    override fun onMessageReceived(message: RemoteMessage) {

        if (PushMessageManager.isMarketingCloudPush(message)) {
            SFMCSdk.requestSdk { sdk ->
                sdk.mp {
                    it.pushMessageManager.handleMessage(message)
                }
            }
        } else {
            //Not from Marketing Cloud.  Must handle yourself.
        }
    }
}