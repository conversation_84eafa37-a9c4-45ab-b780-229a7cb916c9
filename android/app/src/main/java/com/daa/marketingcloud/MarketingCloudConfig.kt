package com.daa.marketingcloud

import android.content.Context
import android.util.Log
import com.daa.BuildConfig
import com.daa.R
import com.google.firebase.messaging.FirebaseMessaging
import com.salesforce.marketingcloud.MCLogListener
import com.salesforce.marketingcloud.MarketingCloudConfig
import com.salesforce.marketingcloud.MarketingCloudSdk
import com.salesforce.marketingcloud.notifications.NotificationCustomizationOptions
import com.salesforce.marketingcloud.sfmcsdk.InitializationStatus
import com.salesforce.marketingcloud.sfmcsdk.SFMCSdk
import com.salesforce.marketingcloud.sfmcsdk.SFMCSdkModuleConfig
import com.salesforce.marketingcloud.sfmcsdk.components.logging.LogLevel
import com.salesforce.marketingcloud.sfmcsdk.components.logging.LogListener
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

object MarketingCloudConfig {
    private val TAG = "MarketingCloudConfig"

    fun init(context: Context){
        val sfmcAppId = context.getString(R.string.sfmc_app_id);
        val sfmcAccessToken = context.getString(R.string.sfmc_access_token);
        val sfmcFcmSenderId = context.getString(R.string.sfmc_fcm_sender_id);
        val sfmcServerUrl = context.getString(R.string.sfmc_server_url);

        SFMCSdk.configure(
            context,
            SFMCSdkModuleConfig.build {
                pushModuleConfig =
                    MarketingCloudConfig.builder()
                        .apply {
                            setApplicationId(sfmcAppId)
                            setAccessToken(sfmcAccessToken)
                            setMarketingCloudServerUrl(sfmcServerUrl)
                            setGeofencingEnabled(true)
                            setNotificationCustomizationOptions(
                                NotificationCustomizationOptions.create(
                                    R.mipmap.ic_launcher,
                                ),
                            )

                        }
                        .build(context)
            }
        ) { initStatus ->
            when (initStatus.status) {
                InitializationStatus.SUCCESS -> Log.d(TAG, "SFMC SDK Initialization Successful")
                InitializationStatus.FAILURE -> Log.d(TAG, "SFMC SDK Initialization Failed")
                else -> Log.d(TAG, "SFMC SDK Initialization Status: Unknown")
            }
        }
        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val fcmToken = task.result
                    Log.d("~#SdkState", "Token: $fcmToken")

                    // Pass token to SFMC PushManager (if not handled automatically)
                    SFMCSdk.requestSdk { sdk ->
                        sdk.mp {
                            it.pushMessageManager.setPushToken(fcmToken)
                        }
                        val sdkStateJson = sdk.getSdkState()  // This returns a JSONObject
                        val sdkStateString = sdkStateJson.toString()

                        // Optionally, log or use it
                        Log.i("~#SdkState", sdkStateString)
                        val pushState = sdkStateJson.optJSONObject("PUSH")

                        pushState?.let {
                            // General Troubleshooting
                            Log.i("~#SdkState", "initConfig: ${it.opt("initConfig")}")
                            Log.i("~#SdkState", "initStatus: ${it.opt("initStatus")}")
                            Log.i("~#SdkState", "PushMessageManager: ${it.optJSONObject("PushMessageManager")?.toString(2)}")
                            Log.i("~#SdkState", "RegistrationManager: ${it.optJSONObject("RegistrationManager")?.toString(2)}")

                            // Troubleshoot InApp Messages
                            Log.i("~#SdkState", "InAppMessageManager: ${it.optJSONObject("InAppMessageManager")?.toString(2)}")

                            val inAppMessages = it.optJSONObject("InAppMessageManager")?.optJSONArray("messages")
                            Log.i("~#SdkState", "InApp Messages: ${inAppMessages?.toString(2)}")

                            // Get Everything
                            Log.i("~#SdkState", "InApp Events: ${it.optJSONObject("Event")?.toString(2)}")
                        } ?: Log.w("~#SdkState", "PUSH object not found in SDK state")
                    }
                } else {
                    Log.w("~#SdkState", "Fetching FCM token failed", task.exception)
                }
            }

        
    }
}