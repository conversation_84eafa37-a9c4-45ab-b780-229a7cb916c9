package com.daa

import android.app.Application
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.util.Log
import com.appsflyer.AppsFlyerConsent
import com.appsflyer.AppsFlyerLib
import com.appsflyer.attribution.AppsFlyerRequestListener
import com.daa.marketingcloud.MarketingCloudConfig
import com.daa.utils.CommonUtils
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.soloader.OpenSourceMergedSoMapping
import com.facebook.soloader.SoLoader
import com.salesforce.marketingcloud.UrlHandler
import com.salesforce.marketingcloud.notifications.NotificationManager
import com.salesforce.marketingcloud.notifications.NotificationMessage
import kotlin.random.Random


class MainApplication : Application(), ReactApplication, UrlHandler,
    NotificationManager.NotificationChannelIdProvider,
    NotificationManager.NotificationLaunchIntentProvider {

    override val reactNativeHost: ReactNativeHost =
        object : DefaultReactNativeHost(this) {
            override fun getPackages(): List<ReactPackage> =
                PackageList(this).packages.apply {
                    // Packages that cannot be autolinked yet can be added manually here, for example:
                    // packages.add(new MyReactNativePackage());

                }

            override fun getJSMainModuleName(): String = "index"

            override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

            override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
            override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED
        }
    override val reactHost: ReactHost
        get() = getDefaultReactHost(applicationContext, reactNativeHost)

    override fun onCreate() {
        super.onCreate()
        SoLoader.init(this, OpenSourceMergedSoMapping)
        initAppsFlyer()
        MarketingCloudConfig.init(applicationContext)

        if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
            // If you opted-in for the New Architecture, we load the native entry point for this app.
            load()
        }

    }

    private fun initAppsFlyer() {
        val gdprUser = AppsFlyerConsent(
            isUserSubjectToGDPR = true,
            hasConsentForDataUsage = false,
            hasConsentForAdsPersonalization = false,
            hasConsentForAdStorage = false
        )
        val appsFlyerLib =
            AppsFlyerLib.getInstance().init(getString(R.string.apps_flyer_key), null, this)
        appsFlyerLib.setCustomerUserId(CommonUtils.getDeviceId(applicationContext))
        appsFlyerLib.setConsentData(gdprUser)
        appsFlyerLib
            .start(this, getString(R.string.apps_flyer_key), object : AppsFlyerRequestListener {
                override fun onSuccess() {
                    Log.i(MainApplication::class.simpleName, "apps flyer initiated successfully")
                    appsFlyerLib.stop(true, applicationContext)
                }

                override fun onError(errorCode: Int, errorDesc: String) {
                    Log.i(
                        MainApplication::class.simpleName, "apps flyer failed to be sent:\n" +
                                "Error code: " + errorCode + "\n"
                                + "Error description: " + errorDesc
                    )
                }
            })
    }

    override fun getNotificationChannelId(
        context: Context,
        notificationMessage: NotificationMessage
    ): String {
        return NotificationManager.createDefaultNotificationChannel(context)
    }

    override fun handleUrl(context: Context, url: String, urlSource: String): PendingIntent? {
        return getPendingIntent(context, url)
    }

    override fun getNotificationPendingIntent(
        context: Context,
        notificationMessage: NotificationMessage
    ): PendingIntent {
        return getPendingIntent(context, notificationMessage.url)
    }

    private fun getPendingIntent(context: Context, url: String?): PendingIntent {
        return when {
            url.isNullOrEmpty() ->
                PendingIntent.getActivity(
                    context,
                    Random.nextInt(),
                    context.packageManager.getLaunchIntentForPackage(context.packageName),
                    provideIntentFlags(),
                )

            else ->
                PendingIntent.getActivity(
                    context,
                    Random.nextInt(),
                    Intent(Intent.ACTION_VIEW, Uri.parse(url)),
                    provideIntentFlags(),
                )
        }
    }

    private fun provideIntentFlags(): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
    }
}
