<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission
        android:name="com.google.android.gms.permission.AD_ID"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.ACCESS_ADSERVICES_AD_ID"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION"
        tools:node="remove" />
    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/appsflyer_data_extraction_rules"
        android:fullBackupContent="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:windowSoftInputMode="adjustResize"
        tools:replace="android:dataExtractionRules,android:fullBackupContent,android:allowBackup">
        <meta-data
            android:name="android.supportsRtl"
            android:value="true" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_notification" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
        <meta-data android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
            android:value="true"/>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/primary"
            tools:replace="android:resource" />
        <meta-data
            android:name="google_analytics_adid_collection_enabled"
            android:value="false"
            tools:replace="android:value" />
        <meta-data
            android:name="google_analytics_default_allow_ad_personalization_signals"
            android:value="false"
            tools:replace="android:value" />
        <activity
            android:name=".MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="daa" />
                <data
                    android:host="dev.aeroparker.com"
                    android:pathPrefix="/booking"
                    android:scheme="https" />
                <data
                    android:host="dev.aeroparker.com"
                    android:pathPrefix="/booking"
                    android:scheme="http" />
                <data
                    android:host="prebook.dublinairport.com"
                    android:pathPrefix="/booking"
                    android:scheme="https" />
                <data
                    android:host="prebook.dublinairport.com"
                    android:pathPrefix="/booking"
                    android:scheme="http" />
            </intent-filter>
        </activity>
        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>
</manifest>